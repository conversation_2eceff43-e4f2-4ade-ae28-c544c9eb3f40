<template>
  <div class="rank-container">
    <el-row :gutter="10">
      <el-col>
        <SubTitle title="司机行驶里程排名"></SubTitle>
      </el-col>
      <el-col>
        <TimeScreen class="mt-16"></TimeScreen>
      </el-col>
    </el-row>
    <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" height="600" stripe>
      <el-table-column type="index" label="排名" align="center" width="80"></el-table-column>
      <el-table-column prop="plateNumber" label="司机姓名" align="center"></el-table-column>
      <el-table-column prop="operatorName" label="行驶里程(km)" align="center" sortable></el-table-column>
    </el-table>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        tableData: [],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .rank-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
</style>
