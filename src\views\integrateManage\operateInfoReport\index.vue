<template>
  <div class="operate-info-container micro-app-sctmp_base" :key="refreshKey">
    <header class="header">
      <h2 class="header-title">运营信息报表</h2>
      <el-tabs class="header-tabs" v-model="activeTab">
        <el-tab-pane :label="item" v-for="(item, index) in tabList" :key="index"></el-tab-pane>
      </el-tabs>
    </header>
    <main class="main">
      <Title title="垃圾收运量信息"></Title>
      <div class="rubbish-box">
        <el-row class="mb-12">
          <el-col :xl="12">
            <SubTitle title="今日垃圾收运量"></SubTitle>
          </el-col>
          <el-col :xl="12">
            <TimeScreen class="time-screen"></TimeScreen>
          </el-col>
        </el-row>
        <div class="rubbish-grid-box">
          <div class="rubbish-content mb-16">
            <div class="rubbish-content-title">收运垃圾总重量</div>
            <div class="rubbish-content-box">
              <div class="rubbish-count">
                <div class="count-big">3577998</div>
                <div class="count-unit">（kg）</div>
              </div>
            </div>
          </div>
          <div>
            <el-row :gutter="16">
              <el-col :lg="12" :xl="8" v-for="(item, index) in rubbishList" :key="index">
                <div class="rubbish-content mb-16">
                  <div class="rubbish-content-title">{{ item }}</div>
                  <div class="rubbish-content-box">
                    <div class="rubbish-count">
                      <div class="count-small">3577998</div>
                      <div class="count-unit">（kg）</div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <Title title="里程信息"></Title>
      <div class="mileage-box">
        <div class="mileage-left">
          <MileageRank></MileageRank>
        </div>
        <div class="mileage-right">
          <div class="mileage-right-top">
            <MileageAverage></MileageAverage>
            <MileageComparison></MileageComparison>
          </div>
          <MileageAnomaly></MileageAnomaly>
        </div>
      </div>
      <Title title="安全自检/培训信息"></Title>
      <SafetyCheck></SafetyCheck>
      <SafetyTrain></SafetyTrain>
      <Title title="加油数据信息"></Title>
      <Refuel></Refuel>
      <Title title="司机能力雷达图"></Title>
      <DriverAbility></DriverAbility>
    </main>
  </div>
</template>

<script>
  import Title from "./components/Title.vue";
  import SubTitle from "./components/SubTitle.vue";
  import TimeScreen from "./components/TimeScreen.vue";
  import MileageRank from "./components/MileageRank.vue";
  import MileageAverage from "./components/MileageAverage.vue";
  import MileageComparison from "./components/MileageComparison.vue";
  import MileageAnomaly from "./components/MileageAnomaly.vue";
  import SafetyCheck from "./components/SafetyCheck.vue";
  import SafetyTrain from "./components/SafetyTrain.vue";
  import Refuel from "./components/Refuel.vue";
  import DriverAbility from "./components/DriverAbility.vue";
  export default {
    components: {
      Title,
      SubTitle,
      TimeScreen,
      MileageRank,
      MileageAverage,
      MileageComparison,
      MileageAnomaly,
      SafetyCheck,
      SafetyTrain,
      Refuel,
      DriverAbility,
    },
    data() {
      return {
        activeTab: 0,
        tabList: ["运输管理信息"],
        rubbishList: [
          "感染性废物重量",
          "损伤性废物重量",
          "药物性废物重量",
          "病理性废物重量",
          "化学性废物重量",
          "感染性——污泥重量",
        ],
        timer: null,
        refreshKey: 0,
      };
    },
    mounted() {
      this.$nextTick(() => {
        window.addEventListener("resize", this.handleResize);
      });
    },
    beforeDestroy() {
      // 组件销毁前清除监听器和定时器
      window.removeEventListener("resize", this.handleResize);
      // 清除定时器（在这个例子中可能不是必需的，但展示如何操作）
      if (this.timer !== null) {
        clearTimeout(this.timer);
        this.timer = null; // 重置为null是个好习惯
      }
    },
    methods: {
      handleResize() {
        if (this.timer !== null) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.refreshKey++;
        }, 1000);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .operate-info-container {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    overflow-y: auto;
  }
  .header {
    background-color: #fff;
    padding: 20px 30px 0 30px;
    .header-title {
      text-align: center;
      margin-top: 0;
    }
    ::v-deep .header-tabs .el-tabs__header {
      margin: 0;
    }
  }
  .main {
    padding: 20px;
    padding-top: 0;
  }
  .rubbish-box {
    padding: 16px;
    padding-bottom: 0;
    background-color: #fff;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    .rubbish-grid-box {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(2, 1fr);
      .rubbish-content {
        padding: 16px;
        border: 1px solid #d7d7d7;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        .rubbish-content-title {
          font-size: 14px;
          color: #333;
        }
        .rubbish-content-box {
          flex: 1;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        }
        .rubbish-count {
          display: flex;
          align-items: flex-end;
          .count-big {
            font-size: 50px;
            font-weight: bold;
            line-height: 38px;
          }
          .count-small {
            font-size: 28px;
            line-height: 24px;
          }
          .count-unit {
            font-size: 12px;
          }
        }
      }
    }
  }
  .mileage-box {
    display: grid;
    grid-gap: 16px;
    grid-template-columns: 2fr 6fr;
    .mileage-right-top {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .time-screen {
    display: flex;
    justify-content: flex-end;
  }
</style>
