<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="安全培训统计"></SubTitle>
      </el-col>
      <el-col :xl="12" class="time-screen">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          :clearable="false"
          @change="handleChange"
        ></el-date-picker>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left"></div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }">
          <el-table-column prop="plateNumber" label="季度" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="培训次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="参与人数" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import moment from "moment";
  export default {
    components: {
      SubTitle,
    },
    data() {
      return {
        tableData: [],
        year: moment().format("YYYY"),
      };
    },
    methods: {
      handleChange() {},
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
    margin-top: 20px;
  }
  .time-screen {
    display: flex;
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
