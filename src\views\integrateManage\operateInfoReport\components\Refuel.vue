<template>
  <div class="refuel-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="加油数据统计"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="refuel-main">
      <div class="refuel-chart"></div>
      <div class="refuel-table">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }">
          <el-table-column prop="plateNumber" label="司机类型" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="加油量(升)" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="加油费用(元)" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="加油频次" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="平均每次加油费用(元)" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        tableData: [],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .refuel-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
</style>
